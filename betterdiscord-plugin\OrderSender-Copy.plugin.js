/**
 * @name OrderSender Copy
 * @description Точная копия рабочего плагина с заменой функциональности
 * @version 1.0.0
 * <AUTHOR>
 * @website https://tgapi-pink.vercel.app
 * @source https://raw.githubusercontent.com/yourusername/yourrepo/main/betterdiscord-plugin/OrderSender-Copy.plugin.js
 */

const config = {
    main: "index.js",
    name: "OrderSender Copy",
    author: "YourName",
    version: "1.0.0",
    description: "Точная копия рабочего плагина с заменой функциональности",
    github: "https://github.com/yourusername/yourrepo",
    github_raw: "https://raw.githubusercontent.com/yourusername/yourrepo/main/betterdiscord-plugin/OrderSender-Copy.plugin.js",
    changelog: [],
    defaultConfig: [
        {
            type: "textbox",
            id: "apiUrl",
            name: "URL API",
            note: "URL для отправки сообщений",
            value: "https://tgapi-pink.vercel.app/api/send-to-sheet"
        },
        {
            type: "switch",
            id: "debugMode",
            name: "Режим отладки",
            note: "Показывать отладочную информацию в консоли",
            value: false
        }
    ]
};

class Dummy {
    constructor() {this._config = config;}
    start() {}
    stop() {}
}
 
if (!global.ZeresPluginLibrary) {
    BdApi.showConfirmationModal("Library Missing", `The library plugin needed for ${config.name ?? config.info.name} is missing. Please click Download Now to install it.`, {
        confirmText: "Download Now",
        cancelText: "Cancel",
        onConfirm: () => {
            require("request").get("https://betterdiscord.app/gh-redirect?id=9", async (err, resp, body) => {
                if (err) return require("electron").shell.openExternal("https://betterdiscord.app/Download?id=9");
                if (resp.statusCode === 302) {
                    require("request").get(resp.headers.location, async (error, response, content) => {
                        if (error) return require("electron").shell.openExternal("https://betterdiscord.app/Download?id=9");
                        await new Promise(r => require("fs").writeFile(require("path").join(BdApi.Plugins.folder, "0PluginLibrary.plugin.js"), content, r));
                    });
                }
                else {
                    await new Promise(r => require("fs").writeFile(require("path").join(BdApi.Plugins.folder, "0PluginLibrary.plugin.js"), body, r));
                }
            });
        }
    });
}
 
module.exports = !global.ZeresPluginLibrary ? Dummy : (([Plugin, Api]) => {
     const plugin = (Plugin, Library) => {
    const { Logger, Settings } = Library;
    const { ContextMenu } = window.BdApi;

    return class OrderSenderCopy extends Plugin {
        constructor() {
            super();
            this.settings = this.loadSettings();
        }

        onStart() {
            try {
                this.patchContextMenu();
                Logger.info("Plugin enabled, context menu patched!");
            } catch (err) {
                Logger.info("Errored while patching.");
                Logger.info(err);
            }
        }

        onStop() {
            this.contextMenuPatch?.();
            Logger.info("Plugin disabled, context menu unpatched!");
        }

        patchContextMenu() {
            // Extract callback to global scope as it is required for unpatching
            const callback = (tree, props) => {
                // Проверяем, что это сообщение
                if (!props.message) return;

                const copyGroup = tree.props.children[tree.props.children.length - 1];
                copyGroup.props.children.push(
                    ContextMenu.buildItem({
                        id: "send-to-server",
                        label: "📤 Отправить на сервер",
                        action: async () => {
                            await this.sendMessageToAPI(props.message);
                        },
                    })
                );
            };

            this.contextMenuPatch = ContextMenu.patch(
                "message",
                callback
            );
        }

        async sendMessageToAPI(message) {
            try {
                // Получаем данные через window.BdApi
                const ChannelStore = window.BdApi.Webpack.getStore("ChannelStore");
                const UserStore = window.BdApi.Webpack.getStore("UserStore");
                
                const channel = ChannelStore?.getChannel(message.channel_id);
                const user = UserStore?.getCurrentUser();
                
                if (!channel || !user || !message.content) {
                    if (this.settings.debugMode) {
                        Logger.warn("Недостаточно данных для отправки сообщения");
                    }
                    window.BdApi.showToast("❌ Недостаточно данных для отправки", { type: "error" });
                    return;
                }

                const messageData = {
                    text: message.content,
                    chat: channel.name || channel.id,
                    sender: user.username || user.globalName || "Unknown",
                    userId: user.id,
                    platform: 'discord'
                };

                if (this.settings.debugMode) {
                    Logger.info("Отправляем сообщение на API:", {
                        text: messageData.text.substring(0, 100) + (messageData.text.length > 100 ? "..." : ""),
                        chat: messageData.chat,
                        sender: messageData.sender,
                        userId: messageData.userId
                    });
                }

                const response = await fetch(this.settings.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(messageData)
                });

                if (response.ok) {
                    window.BdApi.showToast("✅ Сообщение отправлено на сервер", { type: "success" });
                    if (this.settings.debugMode) {
                        Logger.info("Сообщение успешно отправлено на API");
                    }
                } else {
                    const errorText = await response.text();
                    window.BdApi.showToast("❌ Ошибка отправки на сервер", { type: "error" });
                    if (this.settings.debugMode) {
                        Logger.warn(`Ошибка API: ${response.status} ${response.statusText}`, errorText);
                    }
                }
            } catch (error) {
                window.BdApi.showToast("❌ Ошибка сети", { type: "error" });
                if (this.settings.debugMode) {
                    Logger.error("Ошибка отправки на API:", error);
                }
            }
        }

        getSettingsPanel() {
            return Settings.SettingPanel.build(this.saveSettings.bind(this), 
                new Settings.Textbox("URL API", "URL для отправки сообщений", this.settings.apiUrl, (value) => {
                    this.settings.apiUrl = value;
                }),
                new Settings.Switch("Режим отладки", "Показывать отладочную информацию в консоли", this.settings.debugMode, (value) => {
                    this.settings.debugMode = value;
                })
            );
        }

        saveSettings() {
            window.BdApi.Data.save(config.name, "settings", this.settings);
        }

        loadSettings() {
            const defaultSettings = {};
            config.defaultConfig.forEach(setting => {
                defaultSettings[setting.id] = setting.value;
            });
            return Object.assign(defaultSettings, window.BdApi.Data.load(config.name, "settings") || {});
        }
    };
};
     return plugin(Plugin, Api);
})(global.ZeresPluginLibrary.buildPlugin(config));
