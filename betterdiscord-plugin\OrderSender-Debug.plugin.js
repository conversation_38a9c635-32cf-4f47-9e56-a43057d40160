/**
 * @name OrderSender Debug
 * @description Отладочная версия для поиска правильного типа контекстного меню
 * @version 1.0.0
 * <AUTHOR>
 * @website https://tgapi-pink.vercel.app
 */

const config = {
    main: "index.js",
    name: "OrderSender Debug",
    author: "YourName",
    version: "1.0.0",
    description: "Отладочная версия для поиска правильного типа контекстного меню",
    github: "https://github.com/yourusername/yourrepo",
    github_raw: "https://raw.githubusercontent.com/yourusername/yourrepo/main/betterdiscord-plugin/OrderSender-Debug.plugin.js",
    changelog: [],
    defaultConfig: []
};

class Dummy {
    constructor() {this._config = config;}
    start() {}
    stop() {}
}
 
if (!global.ZeresPluginLibrary) {
    BdApi.showConfirmationModal("Library Missing", `The library plugin needed for ${config.name} is missing. Please click Download Now to install it.`, {
        confirmText: "Download Now",
        cancelText: "Cancel",
        onConfirm: () => {
            require("request").get("https://betterdiscord.app/gh-redirect?id=9", async (err, resp, body) => {
                if (err) return require("electron").shell.openExternal("https://betterdiscord.app/Download?id=9");
                if (resp.statusCode === 302) {
                    require("request").get(resp.headers.location, async (error, response, content) => {
                        if (error) return require("electron").shell.openExternal("https://betterdiscord.app/Download?id=9");
                        await new Promise(r => require("fs").writeFile(require("path").join(BdApi.Plugins.folder, "0PluginLibrary.plugin.js"), content, r));
                    });
                }
                else {
                    await new Promise(r => require("fs").writeFile(require("path").join(BdApi.Plugins.folder, "0PluginLibrary.plugin.js"), body, r));
                }
            });
        }
    });
}
 
module.exports = !global.ZeresPluginLibrary ? Dummy : (([Plugin, Api]) => {
    const plugin = (Plugin, Library) => {
        const { Logger } = Library;
        const { ContextMenu } = window.BdApi;

        return class OrderSenderDebug extends Plugin {
            onStart() {
                try {
                    this.patchAllContextMenus();
                    Logger.info("Debug plugin enabled, patching all context menus!");
                } catch (err) {
                    Logger.error("Errored while patching:", err);
                }
            }

            onStop() {
                // Отключаем все патчи
                this.patches?.forEach(patch => patch?.());
                Logger.info("Debug plugin disabled, all patches removed!");
            }

            patchAllContextMenus() {
                this.patches = [];
                
                // Список возможных типов контекстных меню
                const menuTypes = [
                    "message",
                    "user-context", 
                    "channel-context",
                    "guild-context",
                    "textarea-context",
                    "message-context",
                    "user-message",
                    "channel-message",
                    "dm-message",
                    "guild-message"
                ];

                menuTypes.forEach(menuType => {
                    try {
                        const callback = (tree, props) => {
                            if (menuType === "message" && props.message) {
                                console.log(`[OrderSender Debug] Детальная структура tree для message:`, tree);
                                console.log(`[OrderSender Debug] tree.props:`, tree.props);
                                console.log(`[OrderSender Debug] tree.props.children:`, tree.props.children);
                                console.log(`[OrderSender Debug] tree.props.value:`, tree.props.value);

                                // Пытаемся разные способы добавления элемента
                                try {
                                    // Способ 1: Через tree.props.children
                                    if (tree.props.children) {
                                        console.log(`[OrderSender Debug] Пробуем способ 1: tree.props.children`);
                                        if (Array.isArray(tree.props.children)) {
                                            tree.props.children.push(
                                                ContextMenu.buildItem({
                                                    id: "debug-method-1",
                                                    label: "🔍 Method 1",
                                                    action: () => window.BdApi.showToast("Method 1 works!", { type: "success" })
                                                })
                                            );
                                            console.log(`[OrderSender Debug] ✅ Method 1 успешно`);
                                        } else {
                                            console.log(`[OrderSender Debug] tree.props.children не массив:`, typeof tree.props.children);
                                        }
                                    }

                                    // Способ 2: Через tree.props.value
                                    if (tree.props.value && Array.isArray(tree.props.value)) {
                                        console.log(`[OrderSender Debug] Пробуем способ 2: tree.props.value`);
                                        tree.props.value.push(
                                            ContextMenu.buildItem({
                                                id: "debug-method-2",
                                                label: "🔍 Method 2",
                                                action: () => window.BdApi.showToast("Method 2 works!", { type: "success" })
                                            })
                                        );
                                        console.log(`[OrderSender Debug] ✅ Method 2 успешно`);
                                    }

                                    // Способ 3: Поиск массива в глубине
                                    const findArrayInObject = (obj, path = "") => {
                                        for (const [key, value] of Object.entries(obj)) {
                                            const currentPath = path ? `${path}.${key}` : key;
                                            if (Array.isArray(value)) {
                                                console.log(`[OrderSender Debug] Найден массив в ${currentPath}:`, value);
                                                try {
                                                    value.push(
                                                        ContextMenu.buildItem({
                                                            id: `debug-method-3-${key}`,
                                                            label: `🔍 Method 3 (${key})`,
                                                            action: () => window.BdApi.showToast(`Method 3 (${key}) works!`, { type: "success" })
                                                        })
                                                    );
                                                    console.log(`[OrderSender Debug] ✅ Method 3 (${key}) успешно`);
                                                } catch (e) {
                                                    console.log(`[OrderSender Debug] Method 3 (${key}) failed:`, e);
                                                }
                                            } else if (typeof value === 'object' && value !== null && path.split('.').length < 3) {
                                                findArrayInObject(value, currentPath);
                                            }
                                        }
                                    };

                                    if (tree.props) {
                                        findArrayInObject(tree.props);
                                    }

                                } catch (addError) {
                                    console.error(`[OrderSender Debug] Ошибка добавления элемента:`, addError);
                                }
                            }
                        };

                        const patch = ContextMenu.patch(menuType, callback);
                        this.patches.push(patch);
                        console.log(`[OrderSender Debug] Патч для "${menuType}" установлен`);
                    } catch (patchError) {
                        console.warn(`[OrderSender Debug] Не удалось установить патч для "${menuType}":`, patchError);
                    }
                });
            }
        };
    };
    return plugin(Plugin, Api);
})(global.ZeresPluginLibrary.buildPlugin(config));
