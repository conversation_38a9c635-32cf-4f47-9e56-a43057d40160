/**
 * @name OrderSender Alternative
 * <AUTHOR>
 * @version 2.1.0
 * @description Альтернативная версия плагина с упрощенным добавлением в контекстное меню
 * @website https://tgapi-pink.vercel.app
 * @source https://github.com/yourusername/yourrepo
 * @updateUrl https://raw.githubusercontent.com/yourusername/yourrepo/main/betterdiscord-plugin/OrderSender-Alternative.plugin.js
 */

const config = {
    main: "index.js",
    name: "OrderSender Alternative",
    author: "YourName",
    version: "2.1.0",
    description: "Альтернативная версия плагина с упрощенным добавлением в контекстное меню",
    github: "https://github.com/yourusername/yourrepo",
    github_raw: "https://raw.githubusercontent.com/yourusername/yourrepo/main/betterdiscord-plugin/OrderSender-Alternative.plugin.js",
    changelog: [],
    defaultConfig: [
        {
            type: "textbox",
            id: "apiUrl",
            name: "URL API",
            note: "URL для отправки сообщений",
            value: "https://tgapi-pink.vercel.app/api/send-to-sheet"
        },
        {
            type: "switch",
            id: "debugMode",
            name: "Режим отладки",
            note: "Показывать отладочную информацию в консоли",
            value: false
        }
    ]
};

class Dummy {
    constructor() {this._config = config;}
    start() {}
    stop() {}
}

if (!global.ZeresPluginLibrary) {
    BdApi.showConfirmationModal("Library Missing", `The library plugin needed for ${config.name} is missing. Please click Download Now to install it.`, {
        confirmText: "Download Now",
        cancelText: "Cancel",
        onConfirm: () => {
            require("request").get("https://betterdiscord.app/gh-redirect?id=9", async (err, resp, body) => {
                if (err) return require("electron").shell.openExternal("https://betterdiscord.app/Download?id=9");
                if (resp.statusCode === 302) {
                    require("request").get(resp.headers.location, async (error, response, content) => {
                        if (error) return require("electron").shell.openExternal("https://betterdiscord.app/Download?id=9");
                        await new Promise(r => require("fs").writeFile(require("path").join(BdApi.Plugins.folder, "0PluginLibrary.plugin.js"), content, r));
                    });
                }
                else {
                    await new Promise(r => require("fs").writeFile(require("path").join(BdApi.Plugins.folder, "0PluginLibrary.plugin.js"), body, r));
                }
            });
        }
    });
}

module.exports = !global.ZeresPluginLibrary ? Dummy : (([Plugin, Api]) => {
    const plugin = (Plugin, Library) => {
        const { Logger, Settings } = Library;
        const { ContextMenu } = window.BdApi;

        return class OrderSenderAlternative extends Plugin {
            constructor() {
                super();
                this.settings = this.loadSettings();
            }

            onStart() {
                try {
                    this.patchContextMenu();
                    Logger.info("OrderSender Alternative плагин запущен, контекстное меню пропатчено!");
                } catch (err) {
                    Logger.error("Ошибка при патчинге контекстного меню:", err);
                }
            }

            onStop() {
                this.contextMenuPatch?.();
                Logger.info("OrderSender Alternative плагин остановлен, контекстное меню отпатчено!");
            }

            patchContextMenu() {
                const callback = (tree, props) => {
                    try {
                        if (!props.message) return;
                        if (!tree || !tree.props) return;

                        if (this.settings?.debugMode) {
                            console.log("[OrderSender Alt] Патчим контекстное меню для сообщения:", props.message.id);
                        }

                        // Создаем элемент меню
                        const menuItem = ContextMenu.buildItem({
                            id: "send-to-server",
                            label: "📤 Отправить на сервер",
                            action: () => {
                                this.sendMessageToAPI(props.message);
                            },
                        });

                        // Простое добавление в корень меню
                        if (!tree.props.children) {
                            tree.props.children = [];
                        }

                        // Добавляем разделитель если есть другие элементы
                        if (tree.props.children.length > 0) {
                            tree.props.children.push(ContextMenu.buildItem({
                                type: "separator"
                            }));
                        }

                        // Добавляем наш элемент
                        tree.props.children.push(menuItem);

                        if (this.settings?.debugMode) {
                            console.log("[OrderSender Alt] Элемент меню добавлен успешно");
                        }
                    } catch (error) {
                        console.error("[OrderSender Alt] Ошибка в callback контекстного меню:", error);
                    }
                };

                this.contextMenuPatch = ContextMenu.patch("message", callback);
            }

            async sendMessageToAPI(message) {
                try {
                    // Получаем данные через window.BdApi
                    const ChannelStore = window.BdApi.Webpack.getStore("ChannelStore");
                    const UserStore = window.BdApi.Webpack.getStore("UserStore");
                    
                    const channel = ChannelStore?.getChannel(message.channel_id);
                    const user = UserStore?.getCurrentUser();
                    
                    if (!channel || !user || !message.content) {
                        if (this.settings.debugMode) {
                            Logger.warn("Недостаточно данных для отправки сообщения");
                        }
                        window.BdApi.showToast("❌ Недостаточно данных для отправки", { type: "error" });
                        return;
                    }

                    const messageData = {
                        text: message.content,
                        chat: channel.name || channel.id,
                        sender: user.username || user.globalName || "Unknown",
                        userId: user.id,
                        platform: 'discord'
                    };

                    if (this.settings.debugMode) {
                        Logger.info("Отправляем сообщение на API:", {
                            text: messageData.text.substring(0, 100) + (messageData.text.length > 100 ? "..." : ""),
                            chat: messageData.chat,
                            sender: messageData.sender,
                            userId: messageData.userId
                        });
                    }

                    const response = await fetch(this.settings.apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(messageData)
                    });

                    if (response.ok) {
                        window.BdApi.showToast("✅ Сообщение отправлено на сервер", { type: "success" });
                        if (this.settings.debugMode) {
                            Logger.info("Сообщение успешно отправлено на API");
                        }
                    } else {
                        const errorText = await response.text();
                        window.BdApi.showToast("❌ Ошибка отправки на сервер", { type: "error" });
                        if (this.settings.debugMode) {
                            Logger.warn(`Ошибка API: ${response.status} ${response.statusText}`, errorText);
                        }
                    }
                } catch (error) {
                    window.BdApi.showToast("❌ Ошибка сети", { type: "error" });
                    if (this.settings.debugMode) {
                        Logger.error("Ошибка отправки на API:", error);
                    }
                }
            }

            getSettingsPanel() {
                return Settings.SettingPanel.build(this.saveSettings.bind(this), 
                    new Settings.Textbox("URL API", "URL для отправки сообщений", this.settings.apiUrl, (value) => {
                        this.settings.apiUrl = value;
                    }),
                    new Settings.Switch("Режим отладки", "Показывать отладочную информацию в консоли", this.settings.debugMode, (value) => {
                        this.settings.debugMode = value;
                    })
                );
            }

            saveSettings() {
                window.BdApi.Data.save(config.name, "settings", this.settings);
            }

            loadSettings() {
                const defaultSettings = {};
                config.defaultConfig.forEach(setting => {
                    defaultSettings[setting.id] = setting.value;
                });
                return Object.assign(defaultSettings, window.BdApi.Data.load(config.name, "settings") || {});
            }
        };
    };
    return plugin(Plugin, Api);
})(global.ZeresPluginLibrary.buildPlugin(config));
